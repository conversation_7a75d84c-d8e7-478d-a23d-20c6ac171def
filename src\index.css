@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply antialiased;
  }
}

@layer components {
  .text-balance {
    text-wrap: balance;
  }
  
  .container {
    @apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8;
  }
  
  .btn-primary {
    @apply inline-flex items-center justify-center rounded-md bg-blkout-primary px-4 py-2 text-sm font-medium text-white shadow transition-colors hover:bg-blkout-warm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blkout-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-secondary {
    @apply inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blkout-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }
}