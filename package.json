{"name": "blkout-website", "version": "0.1.0", "description": "Community platform for cooperative ownership - Building bridges, not walls", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@radix-ui/react-slot": "^1.0.0", "@tailwindcss/typography": "^0.5.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "framer-motion": "^11.0.0", "lucide-react": "^0.408.0", "next": "^15.0.0", "postcss": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.4.0", "typescript": "^5.0.0", "vite": "^7.0.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.0", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/blkoutuk/blkout-website.git"}, "keywords": ["cooperative", "community", "black-queer-liberation", "next.js", "typescript"], "author": "BLKOUTUK Community", "license": "Community-Owned"}