# 🏳️‍🌈 BLKOUTUK Website

Community platform for cooperative ownership - Building bridges, not walls.

## Quick Start

```bash
# Clone and setup
git clone https://github.com/[username]/blkout-website.git
cd blkout-website

# Install dependencies
npm install

# Start development server
npm run dev
```

## Development Philosophy

- **"Move at the speed of trust"** - Iterative, community-reviewed releases
- **"Small is good, small is all"** - Incremental improvements over massive changes
- **"Trust the people"** - Community feedback drives development priorities
- **"Focus on critical connections"** - Build features that strengthen community bonds

## Tech Stack

- **Next.js 15** with App Router
- **TypeScript** for community accountability
- **Tailwind CSS** with BLKOUT brand system
- **Shipixen + PageAI** for rapid iteration
- **Framer Motion** for purposeful animations

## Project Structure

```
src/
├── app/                    # Next.js App Router
├── components/
│   ├── landing/           # PageAI/Shipixen components
│   ├── blkout/           # Custom BLKOUT components
│   └── ui/               # Shadcn UI components
├── lib/                  # Utilities & constants
└── content/              # MDX content & stories
```

## Community Values

✊🏿 **Black queer liberation first**  
🤝 **Cooperative ownership**  
🌈 **Authentic community building**  
🔓 **Digital sovereignty**  
💝 **Trust-based development**

## Commands

```bash
npm run dev          # Start development
npm run build        # Build for production
npm run type-check   # TypeScript validation
npm run format       # Code formatting
```

## Contributing

This is a community-owned project. All contributions should center Black queer liberation and cooperative values.

---

*"Building the world we want to live in, one commit at a time."*